terraform {
  required_version = ">= 0.12.0"
}

locals {
  branch_slug = replace(var.app_branch_name, "/", "-")
}

################################################################################################################################
# Terraform Backend Setup
################################################################################################################################

terraform {
  backend "s3" {
    bucket  = "thankview-qa-terraform"
    key     = "ec2/terraform.tfstate"
    region  = "us-east-1"
  }
}


################################################################################################################################
# AWS Setup
################################################################################################################################

provider "aws" {
  profile    = var.aws_profile
  region     = var.aws_region
}

################################################################################################################################
# Outputs
################################################################################################################################

output "app_instance_url" {
  value = module.app_record

  depends_on = [module.app_record]
}

output "app_instance_id" {
  value = module.app_instance.id
  depends_on = [module.app_instance]
}

output "builder_instance_url" {
  value = module.builder_record

  depends_on = [module.builder_record]
}

output "builder_instance_id" {
  value = module.builder_instance.id
  depends_on = [module.builder_instance]
}


################################################################################################################################
# Route 53
################################################################################################################################

module "app_record" {
  source = "terraform-aws-modules/route53/aws//modules/records"
  version = "2.0.0"

  zone_id = var.app_route53_zone_id

  records = [
    {
      name = var.subdomain_name
      type = "A"
      ttl  = 60
      records = [
        module.app_instance.public_ip,
      ]
    }
  ]

   depends_on = [module.app_instance]
}

module "builder_record" {
  source = "terraform-aws-modules/route53/aws//modules/records"
  version = "2.0.0"

  zone_id = var.builder_route53_zone_id

  records = [
    {
      name = var.subdomain_name
      type = "A"
      ttl  = 60
      records = [
        module.builder_instance.public_ip,
      ]
    }
  ]

   depends_on = [module.builder_instance]
}


################################################################################################################################
# EC2 Instances
################################################################################################################################

# QA App Instance
module "app_instance" {
  source                      = "terraform-aws-modules/ec2-instance/aws"
  ami                         = var.app_ami
  name                        = "thankview-qa-app_${local.branch_slug}"
  subnet_id                   = var.app_subnet
  instance_type               = "t3.medium"
  iam_instance_profile        = var.iam_instance_profile
  key_name                    = var.ec2_key_pair_name
  vpc_security_group_ids      = [var.app_sg]
  root_block_device = {
    volume_type = "gp2"
    volume_size = 100
  }
  user_data = <<-EOT
  #!/bin/bash
  BRANCH_NAME=${var.app_branch_name}
  CERT_EMAIL=${var.ssl_cert_email}
  BUILDER_HOST=${var.subdomain_name}.${var.builder_domain_name}
  APP_HOST=${var.subdomain_name}.${var.app_domain_name}
  ${file("${path.root}/user_data/app_instance.sh")}
  EOT
  
  tags = {
    terraform               = "true"
    Application             = "thankview-qa"
    "ThankView::qa::branch" = var.app_branch_name
    "ThankView::qa::owner"  = var.user_name
    VantaNonProd            = true
    VantaNoAlert            = "This is a temporary test environment."
  }
}

# Builder Instance
module "builder_instance" {
  source                      = "terraform-aws-modules/ec2-instance/aws"
  ami                         = var.builder_ami
  name                        = "thankview-qa-builder_${local.branch_slug}"
  subnet_id                   = var.builder_subnet
  instance_type               = "t3.medium"
  iam_instance_profile        = var.iam_instance_profile
  key_name                    = var.ec2_key_pair_name
  vpc_security_group_ids      = [var.builder_sg]
  root_block_device = {
    volume_type = "gp2"
    volume_size = 100
  }
  user_data = <<-EOT
  #!/bin/bash
  BRANCH_NAME=${var.builder_branch_name}
  CERT_EMAIL=${var.ssl_cert_email}
  BUILDER_HOST=${var.subdomain_name}.${var.builder_domain_name}
  APP_HOST=${var.subdomain_name}.${var.app_domain_name}
  ${file("${path.root}/user_data/builder_instance.sh")}
  EOT
  
  tags = {
    terraform               = "true"
    Application             = "thankview-qa"
    "ThankView::qa::branch" = var.app_branch_name
    "ThankView::qa::owner"  = var.user_name
    VantaNonProd            = true
    VantaNoAlert            = "This is a temporary test environment."
  }
}

