#!/bin/bash

# Setup apache
a2ensite builders
systemctl reload apache2
cd /var/www/builders

# Get .env and .gitconfig files from parameter store
aws ssm get-parameter --name "/thankview-qa/builder-env" --with-decryption --output text | cut -d$'\t' -f7 > .env
sed -i -e "s@{app_url}@https:\/\/$APP_HOST@g" -e "s@{builder_host}@$BUILDER_HOST@g" -e "s@{builder_url}@https:\/\/$BUILDER_HOST@g" .env

# Checkout selected branch
git checkout staging
git pull
git checkout $BRANCH_NAME
cp public/.htaccess.staging public/.htaccess

# Composer/Laravel init
su -c "composer install" ssm-user
su -c "composer dump-autoload" ssm-user
php artisan cache:clear
php artisan migrate
php artisan db:seed
php artisan key:generate
php artisan config:clear

# Reset permissions (this script runs as root)
chown -R ssm-user *
chgrp -R www-data *
find . -type f -exec chmod 664 -- {} +
find . -type d -exec chmod 775 -- {} +
chmod -R 774 ./storage
chmod -R 774 ./bootstrap
chmod -R 774 ./vendor

# Wait for DNS propagation
echo "Waiting for DNS propagation before generating SSL certificate..."
sleep 60

# Generate SSL cert with retry logic
echo "Attempting to generate SSL certificate for $BUILDER_HOST..."
if ! certbot --apache -n -d $BUILDER_HOST --agree-tos --email $CERT_EMAIL -v; then
  echo "First attempt failed. Waiting 2 minutes before retrying..."
  sleep 120
  certbot --apache -n -d $BUILDER_HOST --agree-tos --email $CERT_EMAIL -v
fi

# Verify SSL certificate was created
if [ -d "/etc/letsencrypt/live/$BUILDER_HOST" ]; then
  echo "SSL certificate successfully created for $BUILDER_HOST"
else
  echo "WARNING: SSL certificate creation may have failed for $BUILDER_HOST"
  
  # Try to fix Apache configuration if it exists but is not enabled
  if [ -f "/etc/apache2/sites-available/builders-le-ssl.conf" ] && [ ! -f "/etc/apache2/sites-enabled/builders-le-ssl.conf" ]; then
    echo "Enabling SSL configuration..."
    a2ensite builders-le-ssl.conf
    systemctl reload apache2
  fi
fi
