#!/bin/bash

# Setup apache
a2ensite thank-views
systemctl reload apache2
cd /var/www/thank-views

# Get .env, .gitconfig, and Google cloud config files from parameter store
aws ssm get-parameter --name "/thankview-qa/thankview-env" --with-decryption --output text | cut -d$'\t' -f7 > .env
sed -i -e "s@{app_url}@https:\/\/$APP_HOST@g" -e "s@{app_host}@$APP_HOST@g" -e "s@{builder_url}@https:\/\/$BUILDER_HOST@g" .env
mkdir ./keys
aws ssm get-parameter --name "/thankview-qa/Thankview-Google-Cloud-Storage-Key.json" --with-decryption --output text | cut -d$'\t' -f7 > ./keys/Thankview-Google-Cloud-Storage-Key.json

# Checkout selected branch
git checkout staging
git pull
git checkout $BRANCH_NAME
cp public/.htaccess.staging public/.htaccess

# Composer/Laravel init
su -c "composer install" ssm-user
su -c "composer dump-autoload" ssm-user
php artisan clear-compiled
php artisan optimize
php artisan static:clear
php artisan key:generate
php artisan config:clear

# Reset permissions (this script runs as root)
usermod -a -G www-data ssm-user
chown -R ssm-user:www-data /var/www
find /var/www -type f -exec chmod 644 {} \;
find /var/www -type d -exec chmod 775 {} \;
chgrp -R www-data /var/www/thank-views/storage /var/www/thank-views/bootstrap /var/www/thank-views/vendor /var/www/thank-views/public
chmod -R ug+rwx /var/www/thank-views/storage /var/www/thank-views/bootstrap /var/www/thank-views/vendor /var/www/thank-views/public

# Wait for DNS propagation
echo "Waiting for DNS propagation before generating SSL certificate..."
sleep 60

# Generate SSL cert with retry logic
echo "Attempting to generate SSL certificate for $APP_HOST..."
if ! certbot --apache -n -d $APP_HOST --agree-tos --email $CERT_EMAIL -v; then
  echo "First attempt failed. Waiting 2 minutes before retrying..."
  sleep 120
  certbot --apache -n -d $APP_HOST --agree-tos --email $CERT_EMAIL -v
fi

# Verify SSL certificate was created
if [ -d "/etc/letsencrypt/live/$APP_HOST" ]; then
  echo "SSL certificate successfully created for $APP_HOST"
else
  echo "WARNING: SSL certificate creation may have failed for $APP_HOST"
  
  # Try to fix Apache configuration if it exists but is not enabled
  if [ -f "/etc/apache2/sites-available/thank-views-le-ssl.conf" ] && [ ! -f "/etc/apache2/sites-enabled/thank-views-le-ssl.conf" ]; then
    echo "Enabling SSL configuration..."
    a2ensite thank-views-le-ssl.conf
    systemctl reload apache2
  fi
fi
