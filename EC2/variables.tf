# Required variables
variable "aws_profile" {
  type        = string
  description = "(Required) AWS CLI profile to use for authentication"
}

variable "created_by" {
  type        = string
  description = "(Required) The full name of the person who is created the resource in terraform"
}

variable "ssl_cert_email" {
  type        = string
  description = "(Required) Email address used for LetsEncrypt SSL certs"
}

variable "builder_branch_name" {
  type        = string
  description = "(Required) Git branch name for this builder instance"
}

variable "app_branch_name" {
  type        = string
  description = "(Required) Git branch name for this app instance"
}

variable "subdomain_name" {
  type        = string
  description = "(Required) Subdomain name for both instances"
}


# Default variables
variable "aws_region" {
  type        = string
  description = "AWS region in which the VPC and all rersources will be created in"
  default     = "us-east-1"
}

variable "iam_instance_profile" {
  type        = string
  description = "IAM Instance Profile name for EC2 instances"
  default     = "thankview-qa-instance-role"
}

variable "ec2_key_pair_name" {
  type        = string
  description = "Key pair name name for EC2 instances"
  default     = "thankview-qa-ec2"
}

variable "builder_ami" {
  type        = string
  description = "AMI to use for builder instances"
  #default     = "ami-0fa384c10be8c8652" #old v2
  default     = "ami-0bd98efcf937aadad"
}

variable "builder_domain_name" {
  type        = string
  description = "Domain name for builder instance"
  default     = "buildtest-thankview.com"
}

variable "builder_route53_zone_id" {
  type        = string
  description = "Route53 hosted zone for target domain for builder instance"
  default     = "Z00624383T2IMRO536JFA"
}

variable "builder_sg" {
  type        = string
  description = "Security Group ID to use for builder instances"
  default     = "sg-0f99b9ef7efa085b6"
}

variable "builder_subnet" {
  type        = string
  description = "Subnet ID to use for builder instances"
  default     = "subnet-0063acbd6ff74ff61"
}

variable "app_ami" {
  type        = string
  description = "AMI to use for app instances"
  #default     = "ami-036367592907c7544" #old
  default     = "ami-08103dee11d145249"
}

variable "app_domain_name" {
  type        = string
  description = "Domain name for app instances"
  default     = "test-thankview.com"
}

variable "app_route53_zone_id" {
  type        = string
  description = "Route53 hosted zone for target domain for app instances"
  default     = "Z09888951BVOV4EQWKJUQ"
}

variable "app_sg" {
  type        = string
  description = "Security Group ID to use for app instances"
  default     = "sg-0f99b9ef7efa085b6"
}
variable "app_subnet" {
  type        = string
  description = "Subnet ID to use for app instances"
  default     = "subnet-0063acbd6ff74ff61"
}

variable "user_name" {
  description = "Username in format firstInitialLastName (e.g., jsmith for John Smith)"
  type        = string
  
  # No default value - this will force Terraform to prompt for input during apply
}
