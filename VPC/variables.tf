variable "aws_region" {
  type        = string
  description = "(Required) AWS region in which the VPC and all rersources will be created in"
  default     = "us-east-1"
}

variable "aws_profile" {
  type        = string
  description = "(Required) AWS CLI profile to use for authentication"
}

variable "created_by" {
  type        = string
  description = "(Required) the full name of the person who is created the resource in terraform"
}
