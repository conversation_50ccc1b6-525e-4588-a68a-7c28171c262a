terraform {
  required_version = ">= 0.12.0"
}

################################################################################################################################
# Terraform Backend Setup
################################################################################################################################

terraform {
  backend "s3" {
    bucket  = "thankview-qa-terraform"
    key     = "vpc/terraform.tfstate"
    region  = "us-east-1"
  }
}


################################################################################################################################
# AWS Setup
################################################################################################################################

provider "aws" {
  profile    = var.aws_profile
  region     = var.aws_region
}


################################################################################################################################
# IAM Modules
################################################################################################################################

module "aws_ssm_role" {
  source = "./modules/thankview/iam_roles/ec2_instance_role"

  created_by = var.created_by
}


################################################################################################################################
# VPC Modules
################################################################################################################################

module "vpc" {
  source = "terraform-aws-modules/vpc/aws"

  name                        = "thankview-qa-vpc"
  azs                         = ["us-east-1a", "us-east-1b"]
  cidr                        = "172.28.0.0/16"
  private_subnets             = ["172.28.0.0/24", "172.28.1.0/24"]
  public_subnets              = ["172.28.10.0/24","172.28.11.0/24"]

  tags = {
    terraform   = "true"
    created_by  = var.created_by
    Application = "thankview-qa"
  }
}


################################################################################################################################
# CloudTrail Modules
################################################################################################################################

module "cloudtrail" {
  source = "./modules/aws/cloudtrail"

  acl           = "private"
  bucket_prefix = "thankview-poc-cloudtrail-"
  mfa_delete    = false
}


################################################################################################################################
# Security Group Modules
################################################################################################################################

module "app_server_sg" {
  source              = "./modules/thankview/security_groups/app_server_sg"
  cidr_blocks         = ["172.0.0.0/8"]
  rdp_cidr_blocks     = ["172.0.0.0/8"]
  name                = "ThankViewQA_App_SG"
  created_by          = var.created_by  
  vpc_id              = module.vpc.vpc_id
}

module "web_server_sg" {
  source              = "./modules/thankview/security_groups/web_server_sg"
  cidr_blocks         = ["172.0.0.0/8"]
  rdp_cidr_blocks     = ["172.0.0.0/8"]
  name                = "ThankViewQA_Web_SG"
  created_by          = var.created_by 
  vpc_id              = module.vpc.vpc_id
}
