variable "cidr_blocks" {
  description = "security group allowed cidr blocks"
  type        = list
}

variable "name" {
  description = "Name of the security group"
  default     = "domain_controller_sg"
}

variable "created_by" {
  type        = string
  description = "(Required) the full name of the person who is created the resource in terraform"
}

variable "rdp_cidr_blocks" {
  description = "cidr blocks to allow RDP access from other security groups"
  type        = list
}

variable "vpc_id" {
  description = "The VPC id to add the security group"
}
