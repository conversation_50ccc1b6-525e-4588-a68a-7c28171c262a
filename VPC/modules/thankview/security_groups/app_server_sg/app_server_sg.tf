resource "aws_security_group" "security_group" {
    description = "Security group for application servers"
    name        = var.name
    tags        = {
      Name        = var.name
      Application = "thankview-qa"
      terraform   = "true"
      created_by  = var.created_by
    }
    vpc_id      = var.vpc_id

    ingress {
        from_port   = 0
        to_port     = 0
        protocol    = "-1"
        cidr_blocks = var.cidr_blocks
    }

    egress {
    from_port       = 0
    to_port         = 0
    protocol        = "-1"
    cidr_blocks     = ["0.0.0.0/0"]
    }
}
