terraform {
  required_version = ">= 0.12.0"
}

resource "aws_iam_role" "this" {
  name               = "thankview-qa-instance-role"
  assume_role_policy = jsonencode({
    "Version": "2012-10-17",
    "Statement": [
      {
        "Sid": "",
        "Effect": "Allow",
        "Principal": {
          "Service": "ec2.amazonaws.com"
        },
        "Action": "sts:AssumeRole"
      }
    ]
  })
    
  inline_policy {
    name = "ssm_get_poicy"

    policy = jsonencode({
      Version = "2012-10-17"
      Statement = [
        {
          Action   = ["ssm:GetParameter"]
          Effect   = "Allow"
          Resource = "arn:aws:ssm:*:*:parameter/thankview-qa/*"
        },
        {
          Action   = ["kms:Decrypt"]
          Effect   = "Allow"
          Resource = "*"
        },
      ]
    })
  }

  tags = {
    Application = "thankview-qa"
    terraform   = "true"
    created_by  = var.created_by
  }
}

resource "aws_iam_role_policy_attachment" "this_attach_1" {
  role       = aws_iam_role.this.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

resource "aws_iam_role_policy_attachment" "this_attach_2" {
  role       = aws_iam_role.this.name
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
}

resource "aws_iam_instance_profile" "this" {
  name = "thankview-qa-instance-role"
  role = aws_iam_role.this.name
}
