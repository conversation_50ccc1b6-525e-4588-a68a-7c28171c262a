AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: >
  thankview-qa-bitbucket-api

  Application to stop QA instances when Bitbucket branch is deleted

Globals:
  Function:
    Timeout: 30
Parameters:
  Environment:
    Type: String
    Default: dev

Resources:
  # Lambda functions
  HandleUpdateFunction:
    Type: AWS::Serverless::Function 
    Properties:
      CodeUri: handle_update/
      Handler: app.lambda_handler
      Runtime: python3.8
      Role:
        !GetAtt 
          - 'HandleUpdateFunctionRole'
          - 'Arn'
      Environment:
        Variables:
          environment:
            !Ref Environment
      Events:
        HandleUpdate:
          Type: Api 
          Properties:
            Path: /update
            Method: post

  # Roles
  HandleUpdateFunctionRole:
    Type: AWS::IAM::Role
    Properties: 
      AssumeRolePolicyDocument: 
          Version: "2012-10-17"
          Statement:
          -
            Effect: "Allow"
            Principal:
              Service:
                  - "lambda.amazonaws.com" 
            Action: 
            - "sts:AssumeRole"      
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole # provides write permissions to cloudwatch logs
        - !Ref PolicySSMRead
        - !Ref PolicyEC2
      Path: "/service-role/"

  # Policies
  PolicySSMRead:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          -
            Effect: "Allow"
            Action:
              - "ssm:DescribeParameters"
              - "ssm:GetParameter*"
            Resource:
              !Join
                - ''
                - - 'arn:aws:ssm:'
                  - !Ref AWS::Region
                  - ':'
                  - !Ref AWS::AccountId
                  - ':parameter/thankview-qa/bitbucket*'
  
  PolicyEC2:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      PolicyDocument:
        Version: "2012-10-17"
        Statement:
          -
            Effect: "Allow"
            Action:
              - "ec2:StopInstances"
              - "ec2:DescribeInstances"
            Resource: "*"

Outputs:
  WebhookAPIEndpoint:
    Description: "API Gateway endpoint URL for Bitbucket webhook"
    Value: !Sub "https://${ServerlessRestApi}.execute-api.${AWS::Region}.amazonaws.com/Prod/update/"

