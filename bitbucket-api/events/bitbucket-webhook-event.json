{"body": "{      \"actor\": \"User\",  \"repository\": \"Repository\",  \"push\": {    \"changes\": [      {        \"new\": null,        \"old\": {          \"type\": \"branch\",          \"name\": \"development/testing-servers\",          \"target\": {            \"type\": \"commit\",            \"hash\": \"1e65c05c1d5171631d92438a13901ca7dae9618c\",            \"author\": \"User\",            \"message\": \"old commit message\",            \"date\": \"2015-06-08T21:34:56+00:00\",            \"parents\": [              {                \"type\": \"commit\",                \"hash\": \"e0d0c2041e09746be5ce4b55067d5a8e3098c843\",                \"links\": {                  \"self\": {                    \"href\": \"https://api.bitbucket.org/2.0/repositories/user_name/repo_name/commit/9c4a3452da3bc4f37af5a6bb9c784246f44406f7\"                  },                  \"html\": {                    \"href\": \"https://bitbucket.org/user_name/repo_name/commits/9c4a3452da3bc4f37af5a6bb9c784246f44406f7\"                  }                }              }            ],            \"links\": {              \"self\": {                \"href\": \"https://api.bitbucket.org/2.0/repositories/user_name/repo_name/commit/b99ea6dad8f416e57c5ca78c1ccef590600d841b\"              },              \"html\": {                \"href\": \"https://bitbucket.org/user_name/repo_name/commits/b99ea6dad8f416e57c5ca78c1ccef590600d841b\"              }            }          },          \"links\": {            \"self\": {              \"href\": \"https://api.bitbucket.org/2.0/repositories/user_name/repo_name/refs/branches/master\"            },            \"commits\": {              \"href\": \"https://api.bitbucket.org/2.0/repositories/user_name/repo_name/commits/master\"            },            \"html\": {              \"href\": \"https://bitbucket.org/user_name/repo_name/branch/master\"            }          }        },        \"links\": {          \"html\": {            \"href\": \"https://bitbucket.org/user_name/repo_name/branches/compare/c4b2b7914156a878aa7c9da452a09fb50c2091f2..b99ea6dad8f416e57c5ca78c1ccef590600d841b\"          },          \"diff\": {            \"href\": \"https://api.bitbucket.org/2.0/repositories/user_name/repo_name/diff/c4b2b7914156a878aa7c9da452a09fb50c2091f2..b99ea6dad8f416e57c5ca78c1ccef590600d841b\"          },          \"commits\": {            \"href\": \"https://api.bitbucket.org/2.0/repositories/user_name/repo_name/commits?include=c4b2b7914156a878aa7c9da452a09fb50c2091f2&exclude=b99ea6dad8f416e57c5ca78c1ccef590600d841b\"          }        },        \"created\": false,        \"forced\": false,        \"closed\": false,        \"commits\": [          {            \"hash\": \"03f4a7270240708834de475bcf21532d6134777e\",            \"type\": \"commit\",            \"message\": \"commit message\",            \"author\": \"User\",            \"links\": {              \"self\": {                \"href\": \"https://api.bitbucket.org/2.0/repositories/user/repo/commit/03f4a7270240708834de475bcf21532d6134777e\"              },              \"html\": {                \"href\": \"https://bitbucket.org/user/repo/commits/03f4a7270240708834de475bcf21532d6134777e\"              }            }          }        ],        \"truncated\": false      }    ]  }}", "resource": "/{proxy+}", "path": "/path/to/resource", "httpMethod": "POST", "isBase64Encoded": false, "queryStringParameters": {"token": "c1Y1aPOn9dGF9QCCbaeYmCYhNNqL4I1GGNJkQSH2mQPNZ3Hz"}, "pathParameters": {"proxy": "/path/to/resource"}, "stageVariables": {"baz": "qux"}, "headers": {"Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8", "Accept-Encoding": "gzip, deflate, sdch", "Accept-Language": "en-US,en;q=0.8", "Cache-Control": "max-age=0", "CloudFront-Forwarded-Proto": "https", "CloudFront-Is-Desktop-Viewer": "true", "CloudFront-Is-Mobile-Viewer": "false", "CloudFront-Is-SmartTV-Viewer": "false", "CloudFront-Is-Tablet-Viewer": "false", "CloudFront-Viewer-Country": "US", "Host": "**********.execute-api.us-east-1.amazonaws.com", "Upgrade-Insecure-Requests": "1", "User-Agent": "Custom User Agent String", "Via": "1.1 08f323deadbeefa7af34d5feb414ce27.cloudfront.net (CloudFront)", "X-Amz-Cf-Id": "cDehVQoZnx43VYQb9j2-nvCh-9z396Uhbp027Y2JvkCPNLmGJHqlaA==", "X-Forwarded-For": "127.0.0.1, *********", "X-Forwarded-Port": "443", "X-Forwarded-Proto": "https"}, "requestContext": {"accountId": "**********12", "resourceId": "123456", "stage": "prod", "requestId": "c6af9ac6-7b61-11e6-9a41-93e8<PERSON><PERSON><PERSON>f", "requestTime": "09/Apr/2015:12:34:56 +0000", "requestTimeEpoch": *************, "identity": {"cognitoIdentityPoolId": null, "accountId": null, "cognitoIdentityId": null, "caller": null, "accessKey": null, "sourceIp": "127.0.0.1", "cognitoAuthenticationType": null, "cognitoAuthenticationProvider": null, "userArn": null, "userAgent": "Custom User Agent String", "user": null}, "path": "/prod/path/to/resource", "resourcePath": "/{proxy+}", "httpMethod": "POST", "apiId": "**********", "protocol": "HTTP/1.1"}}