import os
import logging
import json
import boto3

logger = logging.getLogger()

env = os.environ.get('environment')
if env == 'dev':
    logger.setLevel(logging.DEBUG)
else:
    logger.setLevel(logging.INFO)

ssm = boto3.client('ssm')
ec2 = boto3.client('ec2')

def lambda_handler(event, context):

    logger.debug(event)

    query_string_params = event.get('queryStringParameters', {})
    request_token = query_string_params.get('token', '')
    webhook_body = json.loads(event.get('body'))

    # Verify secrety token
    config_token = ssm.get_parameter(
        Name=f'/thankview-qa/bitbucket-webhook-secret', WithDecryption=True).get('Parameter').get('Value')

    if request_token != config_token:
        return {
            "statusCode": 403,
            "body": json.dumps({
                "success": False,
            }),
        }

    # Loop through push commits
    changes = webhook_body.get('push', {}).get('changes', [])
    for change in changes:
        logger.debug(change)

        # Determine push type
        old = change.get('old')
        new = change.get('new')

        # This isn't a branch delete - do nothing
        if new != None:
            logger.info("No branch delete - ignoring")
            continue
        
        # This isn't a branch push - do nothing
        if old.get('type') != 'branch':
            logger.info("Not a branch push - ignoring")
            continue

        # Get branch name
        branch_name = old.get('name')
        if branch_name == None:
            logger.info("Branch name missing - ignoring")
            continue

        logger.info(f'Branch {branch_name} deleted - searching for instances')

        # This is a branch delete, find tagged QA instances
        target_instance_ids = []
        qa_tag_filter = [{
            'Name':'tag:ThankView::qa::branch', 
            'Values': [branch_name]
        }]

        response = ec2.describe_instances(Filters=qa_tag_filter)
        logger.debug(response)
        for reservation in response.get('Reservations', []):
            for instance in reservation.get('Instances', []):
                # Get instance information
                instance_id = instance.get('InstanceId')
                logger.debug(f'Found instance {instance_id}')

                # Verify instance name is expcted for QA
                instance_name = ''
                for tag in instance.get('Tags', []):
                    if tag.get('Key') == 'Name':
                        instance_name = tag.get('Value')
                
                # If doesn't contain 'thankview-qa' or branch name, skip it
                logger.debug(f'Found instance name {instance_name}')
                if 'thankview-qa' not in instance_name:
                    continue

                logger.info(f'Found QA instance {instance_id} {instance_name}')
                target_instance_ids.append(instance_id)

        # Stop selected instances
        logger.info(f'Stopping {len(target_instance_ids)} instances...')
        ec2.stop_instances(
            InstanceIds=target_instance_ids,
            DryRun=env == 'dev'
        )

    return {
        "statusCode": 200,
        "body": json.dumps({
            "success": True,
        }),
    }
